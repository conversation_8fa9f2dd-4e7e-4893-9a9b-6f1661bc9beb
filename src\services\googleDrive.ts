// Supported content types for Google Drive uploads
type FileContent = string | Blob | ArrayBuffer | File;

interface UploadOptions {
  fileName: string;
  mimeType?: string;
  content: FileContent;
  appDataFolder?: boolean;
}

interface UpdateOptions extends UploadOptions {
  fileId: string;
}

interface FindOptions {
  fileId?: string;
  spaces?: 'appDataFolder' | 'drive';
  orderBy?: string;
  pageSize?: number;
  query?: string;
}

interface ServiceResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: any;
}

interface DriveFile {
  id: string;
  name: string;
  mimeType: string;
  size?: string;
  createdTime?: string;
  modifiedTime?: string;
}

class GoogleDrive {
  private readonly GOOGLE_DRIVE_API_URL = 'https://www.googleapis.com/drive/v3';
  private accessToken: string | null = null;

  /**
   * Set access token (compatible with old API)
   */
  setAccessToken(token: string): void {
    this.accessToken = token;
  }

  /**
   * Get access token (compatible with old API)
   */
  getAccessToken(): string | null {
    return this.accessToken;
  }

  /**
   * Initialize the Google Drive service with access token
   */
  initialize({ accessToken }: { accessToken: string }) {
    console.log('Initialize Google Drive Service');
    this.accessToken = accessToken;
  }

  /**
   * Get headers for API requests (using Headers object like original)
   */
  private getHeaders(): Headers {
    return new Headers({ Authorization: `Bearer ${this.accessToken}` });
  }

  /**
   * Upload a file to Google Drive
   */
  async upload(options: UploadOptions): Promise<ServiceResponse<DriveFile>> {
    const { fileName, mimeType, content, appDataFolder = false } = options;

    if (!this.accessToken) return { success: false, message: 'not logged in' };
    if (!fileName || !mimeType || !content)
      return {
        success: false,
        message: 'Missing fileName, mimeType or content',
      };

    const API = `${this.GOOGLE_DRIVE_API_URL}/files?uploadType=multipart&fields=id,name,mimeType,size`;
    const metadata = {
      name: fileName,
      mimeType,
      ...(appDataFolder && { parents: ['appDataFolder'] }),
    };
    const form = new FormData();
    form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }));
    form.append('file', new Blob([content], { type: mimeType }));

    try {
      const response = await fetch(API, {
        method: 'POST',
        headers: this.getHeaders(),
        body: form,
      });
      return response.ok
        ? {
            success: true,
            message: 'Upload successfully',
            data: await response.json(),
          }
        : {
            success: false,
            message: 'Upload failed',
            error: await response.json(),
          };
    } catch (err) {
      return { success: false, message: 'An error occurred when uploading', error: err };
    }
  }

  /**
   * Download a file from Google Drive
   */
  async download(fileId: string): Promise<ServiceResponse<any>> {
    if (!this.accessToken) return { success: false, message: 'not logged in' };
    if (!fileId) return { success: false, message: 'Missing fileId' };

    const API = `${this.GOOGLE_DRIVE_API_URL}/files/${fileId}?alt=media`;
    try {
      const response = await fetch(API, {
        method: 'GET',
        headers: this.getHeaders(),
      });
      return response.ok
        ? {
            success: true,
            message: 'Download successfully',
            data: await response.blob(),
          }
        : {
            success: false,
            message: 'Download failed',
            error: await response.json(),
          };
    } catch (error) {
      return { success: false, message: 'An error occurred when downloading', error };
    }
  }

  /**
   * Update an existing file in Google Drive
   */
  async update(options: UpdateOptions): Promise<ServiceResponse<DriveFile>> {
    const { fileId, fileName, mimeType, content } = options;
    if (!this.accessToken) return { success: false, message: 'not logged in' };
    if (!fileId || !content || !fileName || !mimeType)
      return {
        success: false,
        message: 'Missing fileId, content, fileName or mimeType',
      };

    const API = `${this.GOOGLE_DRIVE_API_URL}/files/${fileId}?uploadType=multipart`;
    const metadata = { name: fileName, mimeType };
    const form = new FormData();
    form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }));
    form.append('file', new Blob([content], { type: mimeType }));

    try {
      const response = await fetch(API, {
        method: 'PUT',
        headers: this.getHeaders(),
        body: form,
      });
      return response.ok
        ? {
            success: true,
            message: 'Update successfully',
            data: await response.json(),
          }
        : {
            success: false,
            message: 'Update failed',
            error: await response.json(),
          };
    } catch (error) {
      return { success: false, message: 'An error occurred when updating', error };
    }
  }

  /**
   * Delete a file from Google Drive
   */
  async delete(fileId: string): Promise<ServiceResponse> {
    if (!this.accessToken) return { success: false, message: 'not logged in' };
    if (!fileId) return { success: false, message: 'Missing fileId' };

    const API = `${this.GOOGLE_DRIVE_API_URL}/files/${fileId}`;
    try {
      const response = await fetch(API, {
        method: 'DELETE',
        headers: this.getHeaders(),
      });
      return response.ok
        ? { success: true, message: 'Delete successfully' }
        : { success: false, message: 'Delete failed', error: await response.json() };
    } catch (error) {
      return { success: false, message: 'An error occurred when deleting', error };
    }
  }

  /**
   * Find files in Google Drive (compatible with old get method)
   */
  async get(options: FindOptions): Promise<ServiceResponse<DriveFile | DriveFile[]>> {
    const { fileId, spaces = 'drive', orderBy = 'createdTime desc' } = options;
    if (!this.accessToken) return { success: false, message: 'not logged in' };

    const API = fileId
      ? `${this.GOOGLE_DRIVE_API_URL}/files/${fileId}?fields=id,name,mimeType,size`
      : `${this.GOOGLE_DRIVE_API_URL}/files?spaces=${spaces}&orderBy=${orderBy}`;

    try {
      const response = await fetch(API, {
        method: 'GET',
        headers: this.getHeaders(),
      });
      return response.ok
        ? {
            success: true,
            message: fileId ? 'Get file successfully' : 'Get list of files successfully',
            data: await response.json(),
          }
        : {
            success: false,
            message: 'An error occurred when getting',
            error: await response.json(),
          };
    } catch (error) {
      return { success: false, message: 'Lỗi lấy tệp', error };
    }
  }

  /**
   * Find files in Google Drive (new enhanced method)
   */
  async find(options: FindOptions = {}): Promise<ServiceResponse<DriveFile | DriveFile[]>> {
    const { fileId, spaces = 'drive', orderBy = 'createdTime desc', pageSize = 100, query } = options;
    if (!this.accessToken) return { success: false, message: 'not logged in' };

    let url: string;
    const params = new URLSearchParams();

    if (fileId) {
      // Get specific file
      url = `${this.GOOGLE_DRIVE_API_URL}/files/${fileId}`;
      params.append('fields', 'id,name,mimeType,size,createdTime,modifiedTime');
    } else {
      // List files
      url = `${this.GOOGLE_DRIVE_API_URL}/files`;
      params.append('spaces', spaces);
      params.append('orderBy', orderBy);
      params.append('pageSize', pageSize.toString());
      params.append('fields', 'files(id,name,mimeType,size,createdTime,modifiedTime)');

      if (query) {
        params.append('q', query);
      }
    }

    try {
      const response = await fetch(`${url}?${params.toString()}`, {
        method: 'GET',
        headers: this.getHeaders(),
      });

      return response.ok
        ? {
            success: true,
            message: fileId ? 'File found successfully' : 'Files retrieved successfully',
            data: await response.json(),
          }
        : {
            success: false,
            message: 'Failed to find file(s)',
            error: await response.json(),
          };
    } catch (error) {
      return { success: false, message: 'An error occurred when finding files', error };
    }
  }
}

// Export instance for compatibility with old API
export const googleDrive = new GoogleDrive();

// Also export as drive for backward compatibility
export const drive = googleDrive;
