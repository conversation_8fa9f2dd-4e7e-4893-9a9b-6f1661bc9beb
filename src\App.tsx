import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useEffect } from 'react';
import { BrowserRouter, Outlet, Route, Routes, useLocation, useNavigate } from 'react-router-dom';

import DockNavBar from './components/layouts/DockNavBar';
import { appSettings } from './configs/settings';
import ExpensesPage from './pages/expenses/ExpensesPage';
import SettingsData from './pages/settings/SettingsData';
import SettingsPage from './pages/settings/SettingsPage';
import { ToastProvider } from './providers/ToastProvider';

function Layout() {
  const navigate = useNavigate();
  const location = useLocation();
  const defaultPage = appSettings.general.defaultPage;

  useEffect(() => {
    if (location.pathname === '/') {
      navigate(`/${defaultPage}`);
    }
  }, [defaultPage, location.pathname, navigate]);

  return (
    <div className="flex flex-col h-screen pt-[env(safe-area-inset-top)]">
      <main className="flex-1 overflow-auto no-scrollbar">
        <Outlet />
      </main>
      <DockNavBar />
    </div>
  );
}

export default function App() {
  return (
    <QueryClientProvider client={new QueryClient()}>
      <ToastProvider>
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Layout />}>
              <Route path="expenses" element={<ExpensesPage />} />
              <Route path="statistics" element={<div></div>} />
              <Route path="notes" element={<div></div>} />
              <Route path="settings">
                <Route index element={<SettingsPage />} />
                <Route path="data" element={<SettingsData />} />
              </Route>
            </Route>
          </Routes>
        </BrowserRouter>
      </ToastProvider>
    </QueryClientProvider>
  );
}
